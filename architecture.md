```mermaid
graph TD
    A[Next.js 应用] --> B[前端组件]
    A --> C[后端 API]
    
    B --> D[UI 组件]
    B --> E[业务逻辑]
    
    D --> F[Shadcn UI]
    D --> G[React Flow]
    
    E --> H[状态管理]
    E --> I[事件处理]
    
    C --> J[API 路由]
    C --> K[数据持久化]
```

# 系统架构图

这是一个基于 Next.js 的应用程序架构图，展示了系统的主要组件和它们之间的关系：

1. **前端组件**
   - UI 组件：使用 Shadcn UI 和 React Flow 构建用户界面
   - 业务逻辑：处理状态管理和事件处理

2. **后端 API**
   - API 路由：处理前端请求
   - 数据持久化：管理数据存储

3. **主要技术栈**
   - Next.js 作为主框架
   - Shadcn UI 用于 UI 组件
   - React Flow 用于流程图功能 



## 节点结构更新：
开始节点 (需要输入6位ID,尾号01通过,尾号02不通过)
征信查询节点（原性别判断节点）
额度审核节点（新增决策节点）
人工审核节点（原女性流程节点）
审核通过节点（新增结束节点）
连接关系更新：
开始 → 征信查询(根据尾号来判断)
征信查询 → 额度审核（通过）
征信查询 → 人工审核（不通过）
额度审核 → 审核通过（通过）
额度审核 → 人工审核（不通过）
业务逻辑更新：
在 executeNode 函数中添加了不同节点的处理逻辑
征信查询：据尾号来判断
额度审核：第一次通过,第二次不通过,反复执行
保留了原有的动画和状态更新逻辑
节点属性更新：
添加了 name 属性用于业务逻辑判断
添加了 condition 属性用于显示判断条件
更新了节点的 message 属性以反映贷款审核流程

