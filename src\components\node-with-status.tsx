import { memo, useState, useEffect } from "react";
import { <PERSON>le, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "./base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { toast } from "sonner";

interface CustomNodeData {
    title: string;
    type: string;
    status?: 'success' | 'loading' | 'error';
    order: number;
    gender?: string;
    score?: number;
    [key: string]: unknown;
}

type NodeWithStatusProps = NodeProps<CustomNodeData>;

const NodeWithStatus = memo((props: NodeWithStatusProps) => {
    const { data, id } = props;
    const { setNodes, setEdges, getNodes } = useReactFlow();
    const [status, setStatus] = useState<"success" | "loading" | "error" | undefined>(undefined);
    const [isExecuting, setIsExecuting] = useState(false);

    // 执行节点的函数
    const executeNode = async () => {
        if (isExecuting) return;

        setIsExecuting(true);
        setStatus("loading");

        // 模拟 API 调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        setStatus("success");

        // 显示执行完成提示
        const flowType = data.title.includes("男性") ? "男性流程" : "女性流程";
        toast.success(`执行完成: ${flowType}`);

        // 等待成功状态显示
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 清除当前节点的状态
        setStatus(undefined);
        setIsExecuting(false);

        // 查找下一个节点
        const nextNode = getNodes().find(n =>
            (n.data as CustomNodeData).order === data.order + 1
        );

        // 如果不是最后一个节点，则触发下一个节点
        if (nextNode) {
            setNodes((nodes) =>
                nodes.map((node) => {
                    if (node.id === nextNode.id) {
                        return {
                            ...node,
                            data: {
                                ...node.data,
                                status: "loading",
                                gender: data.gender,
                            },
                        };
                    }
                    return node;
                })
            );
        }
    };

    // 监听父组件传入的 status
    useEffect(() => {
        if (data.status === 'loading' && !isExecuting) {
            executeNode();
        }
    }, [data.status]);

    const onDelete = () => {
        setNodes((nodes) => nodes.filter((node) => node.id !== id));
        setEdges((edges) => edges.filter((edge) => edge.source !== id && edge.target !== id));
    };

    const onExecute = () => {
        // 清除所有节点的状态
        setNodes((nodes) =>
            nodes.map((node) => ({
                ...node,
                data: {
                    ...node.data,
                    status: undefined,
                },
            }))
        );
        // 设置当前节点为 loading
        setNodes((nodes) =>
            nodes.map((node) => {
                if (node.id === id) {
                    return {
                        ...node,
                        data: {
                            ...node.data,
                            status: "loading",
                        },
                    };
                }
                return node;
            })
        );
    };

    const content = (
        <BaseNode {...props} data={data as Record<string, unknown>} id={id}>
            <Handle
                type="target"
                position={Position.Top}
                className="w-3 h-3 bg-primary"
                isConnectable={true}
                id="target"
            />
            <NodeHeader>
                <div className="flex items-center justify-between">
                    <span>{data.title}</span>
                    <div className="flex gap-1">
                        <button
                            className="p-1 hover:bg-accent rounded-md"
                            onClick={onExecute}
                            disabled={isExecuting}
                        >
                            <Play className="w-4 h-4" />
                        </button>
                        <button
                            className="p-1 hover:bg-accent rounded-md"
                            onClick={onDelete}
                            disabled={isExecuting}
                        >
                            <Trash2 className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </NodeHeader>
            {data.score !== undefined && (
                <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
                    <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
                        <span className="text-sm font-medium">信用评分：{data.score}</span>
                    </div>
                </div>
            )}
            <Handle
                type="source"
                position={Position.Bottom}
                className="w-3 h-3 bg-primary"
                isConnectable={true}
                id="source"
            />
        </BaseNode>
    );

    if (status) {
        return <NodeStatusIndicator status={status}>{content}</NodeStatusIndicator>;
    }
    return content;
});

NodeWithStatus.displayName = "NodeWithStatus";

export default NodeWithStatus; 