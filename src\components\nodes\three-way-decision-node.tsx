import { memo, useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";
import { executeNodeWithResult, NodeData } from "@/src/utils/node-executor";

type ThreeWayDecisionNodeProps = NodeProps<NodeData>;

// 新增 ThreeWayDecisionNodeDrawer 组件
function ThreeWayDecisionNodeDrawer({
  isOpen,
  onOpenChange,
  title,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
}) {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>{title}说明</DrawerTitle>
          <DrawerDescription>
            根据风控预审结果进行三路分流处理
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <h4 className="text-md font-semibold mb-2">分流逻辑</h4>
              <div className="border rounded-md overflow-hidden">
                <div className="grid border-b last:border-b-0">
                  <div className="p-2 border-r flex items-center justify-start">
                    分支A：风控评分通过预审标准 → 进入贷款产品匹配流程
                  </div>
                </div>
                <div className="grid border-b last:border-b-0">
                  <div className="p-2 border-r flex items-center justify-start">
                    分支B：风控评分需要人工审核 → 转入人工审核流程
                  </div>
                </div>
                <div className="grid border-b last:border-b-0">
                  <div className="p-2 border-r flex items-center justify-start">
                    分支C：风控评分不符合要求 → 直接拒绝申请
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

const ThreeWayDecisionNode = memo((props: ThreeWayDecisionNodeProps) => {
  const nodeData = props.data as NodeData;
  const { id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [isExecuting, setIsExecuting] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 执行节点的函数
  const executeNode = useCallback(async () => {
    if (isExecuting) return;

    setIsExecuting(true);

    try {
      // 直接从画面上获取信用标准分输入框的值
      const creditCheckScoreInput = document.querySelector(
        'input[placeholder="请输入信用标准分"]'
      ) as HTMLInputElement;
      const creditCheckScore = creditCheckScoreInput
        ? parseInt(creditCheckScoreInput.value) || 600
        : 600;

      // 获取负债比
      const debtRatioInputs = document.querySelectorAll(
        'input[placeholder="负债比"]'
      );
      const debtRatioInput = debtRatioInputs[0] as HTMLInputElement;
      const debtRatio = debtRatioInput
        ? parseInt(debtRatioInput.value) || 75
        : 75;

      // 获取期望差
      const expectedDiffInputs = document.querySelectorAll(
        'input[placeholder="期望差"]'
      );
      const expectedDiffInput = expectedDiffInputs[0] as HTMLInputElement;
      const expectedDifference = expectedDiffInput
        ? parseInt(expectedDiffInput.value) || 5
        : 5;

      await executeNodeWithResult(
        id as string,
        null,
        "threeWayDecisionNode",
        setNodes,
        getNodes,
        getEdges,
        creditCheckScore,
        debtRatio,
        expectedDifference
      );
    } catch (error) {
      console.error("执行节点出错:", error);
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...(node.data as NodeData),
                status: "error",
              },
            };
          }
          return node;
        })
      );
    } finally {
      setIsExecuting(false);
    }
  }, [id, isExecuting, setNodes, getNodes, getEdges]);

  // 监听父组件传入的 status
  useEffect(() => {
    if (nodeData.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [nodeData.status, isExecuting, executeNode]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    if (isExecuting) return;

    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...(node.data as NodeData),
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode
        {...props}
        data={nodeData as Record<string, unknown>}
        id={id as string}
      >
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="target"
        />
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{nodeData.title ?? "三路分流器"}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <div className="p-4">
          <div className="text-sm text-muted-foreground">
            {nodeData.condition || "预审结果分流"}
          </div>
          {nodeData.auditResult !== undefined && (
            <div className="mt-2 text-xs">
              <span className="font-medium">
                决策结果:{" "}
                {nodeData.auditResult === 0
                  ? "自动审核"
                  : nodeData.auditResult === 1
                  ? "人工"
                  : nodeData.auditResult === 2
                  ? "拒绝"
                  : "未知"}
              </span>
            </div>
          )}
        </div>
        <div className="absolute -right-32 top-1/2 transform -translate-y-1/2">
          <div className="bg-white border border-gray-200 rounded-lg px-3 py-2 shadow-sm">
            <div className="flex flex-col gap-1 text-xs">
              <span className="font-medium">
                信用标准分：
                {(() => {
                  const input = document.querySelector(
                    'input[placeholder="请输入信用标准分"]'
                  ) as HTMLInputElement;
                  return input ? input.value : "600";
                })()}
              </span>
              <span className="font-medium">
                负债比：
                {(() => {
                  const inputs = document.querySelectorAll(
                    'input[placeholder="负债比"]'
                  );
                  const input = inputs[0] as HTMLInputElement;
                  return input ? input.value : "75";
                })()}
                %
              </span>
              <span className="font-medium">
                期望差：
                {(() => {
                  const inputs = document.querySelectorAll(
                    'input[placeholder="期望差"]'
                  );
                  const input = inputs[0] as HTMLInputElement;
                  return input ? input.value : "5";
                })()}
                W
              </span>
            </div>
          </div>
        </div>
        <div className="flex justify-between mt-4">
          <Handle
            type="source"
            position={Position.Bottom}
            id="approve"
            className="!bg-green-500 !w-3 !h-3"
            style={{ left: "20%" }}
          />
          <Handle
            type="source"
            position={Position.Bottom}
            id="manual"
            className="!bg-yellow-500 !w-3 !h-3"
            style={{ left: "50%" }}
          />
          <Handle
            type="source"
            position={Position.Bottom}
            id="reject"
            className="!bg-red-500 !w-3 !h-3"
            style={{ left: "80%" }}
          />
        </div>
        <ThreeWayDecisionNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          title={nodeData.title ?? "三路分流器"}
        />
      </BaseNode>
    </div>
  );

  if (
    nodeData.status === "success" ||
    nodeData.status === "error" ||
    nodeData.status === "loading"
  ) {
    return (
      <NodeStatusIndicator status={nodeData.status}>
        {content}
      </NodeStatusIndicator>
    );
  }
  return content;
});

ThreeWayDecisionNode.displayName = "ThreeWayDecisionNode";

export default ThreeWayDecisionNode;
