import { use<PERSON><PERSON>back, useState } from "react";
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  useReactFlow,
} from "@xyflow/react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { toast } from "sonner";
import { TEST_CASES } from "@/src/config/test-data";
import { executeNodeWithResult } from "@/src/utils/node-executor";
import StartNode from "./nodes/start-node";
import EndNode from "./nodes/end-node";
import DecisionNode from "./nodes/decision-node";
import CreditCheckNode from "./nodes/credit-check-node";
import AutoApprovalNode from "./nodes/auto-approval-node";

// 定义节点类型
const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  decisionNode: DecisionNode,
  creditCheckNode: CreditCheckNode,
  autoApprovalNode: AutoApprovalNode,
};

// 初始节点配置
const initialNodes: Node[] = [
  {
    id: "1",
    type: "startNode",
    position: { x: 250, y: 0 },
    data: { title: "开始", type: "startNode", order: 1 },
  },
  {
    id: "2",
    type: "creditCheckNode",
    position: { x: 250, y: 100 },
    data: { title: "征信查询", type: "creditCheckNode", order: 2 },
  },
  {
    id: "3",
    type: "decisionNode",
    position: { x: 250, y: 200 },
    data: { title: "征信查询判定", type: "decisionNode", order: 3 },
  },
  {
    id: "4",
    type: "autoApprovalNode",
    position: { x: 250, y: 300 },
    data: { title: "额度审核", type: "autoApprovalNode", order: 4 },
  },
  {
    id: "5",
    type: "decisionNode",
    position: { x: 250, y: 400 },
    data: { title: "额度审核判定", type: "decisionNode", order: 5 },
  },
  {
    id: "6",
    type: "endNode",
    position: { x: 250, y: 500 },
    data: { title: "结束", type: "endNode", order: 6 },
  },
];

// 初始边配置
const initialEdges: Edge[] = [
  {
    id: "e1-2",
    source: "1",
    target: "2",
    type: "smoothstep",
  },
  {
    id: "e2-3",
    source: "2",
    target: "3",
    type: "smoothstep",
  },
  {
    id: "e3-4",
    source: "3",
    target: "4",
    sourceHandle: "true",
    type: "smoothstep",
  },
  {
    id: "e3-6",
    source: "3",
    target: "6",
    sourceHandle: "false",
    type: "smoothstep",
  },
  {
    id: "e4-5",
    source: "4",
    target: "5",
    type: "smoothstep",
  },
  {
    id: "e5-6",
    source: "5",
    target: "6",
    sourceHandle: "true",
    type: "smoothstep",
  },
  {
    id: "e5-6-false",
    source: "5",
    target: "6",
    sourceHandle: "false",
    type: "smoothstep",
  },
];

export default function FlowCanvas() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [inputValue, setInputValue] = useState("");
  const [creditCheckScore, setCreditCheckScore] = useState("600");
  const { getNodes, getEdges } = useReactFlow();

  const handleExecute = useCallback(() => {
    if (!inputValue) {
      toast.error("请输入用户ID");
      return;
    }

    const testCase = TEST_CASES[inputValue];
    if (!testCase) {
      toast.error("未找到该用户ID的测试数据");
      return;
    }

    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );

    // 设置开始节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === "1") {
          return {
            ...node,
            data: {
              ...node.data,
              status: "loading",
              inputValue: inputValue,
              loanAmount: testCase.loanAmount,
              creditCheckScore: Number(creditCheckScore),
            },
          };
        }
        return node;
      })
    );
  }, [inputValue, creditCheckScore, setNodes]);

  const onConnect = useCallback(
    (params: Connection | Edge) => {
      const existingEdge = edges.find(
        (edge) =>
          edge.source === params.source &&
          edge.sourceHandle === params.sourceHandle
      );

      if (existingEdge) {
        toast.error("该节点已经存在相同类型的连接");
        return;
      }

      setEdges((eds) => addEdge(params, eds));
    },
    [edges, setEdges]
  );

  return (
    <div className="w-full h-full relative">
      <div className="absolute top-4 left-4 z-10 flex flex-col gap-4 bg-background p-4 rounded-lg shadow-lg">
        <div className="flex flex-col gap-2">
          <Label htmlFor="userId">用户ID</Label>
          <div className="flex gap-2">
            <Input
              id="userId"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="请输入用户ID"
              className="w-[200px]"
            />
            <Button onClick={handleExecute}>执行</Button>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="creditCheckScore">征信标准分</Label>
          <Input
            id="creditCheckScore"
            value={creditCheckScore}
            onChange={(e) => setCreditCheckScore(e.target.value)}
            placeholder="请输入征信标准分"
            className="w-[200px]"
            type="number"
            min="0"
            max="1000"
          />
        </div>
      </div>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
} 