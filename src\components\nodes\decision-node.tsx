import { memo, useState, useEffect, useCallback } from "react";
import { Handle, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { executeNodeWithResult, NodeData } from "@/src/utils/node-executor";
import { TEST_CASES } from "@/src/config/test-data";

type DecisionNodeProps = NodeProps<NodeData>;

// 新增 DecisionNodeDrawer 组件
function DecisionNodeDrawer({
  isOpen,
  onOpenChange,
  title,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
}) {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>{title}说明</DrawerTitle>
          <DrawerDescription>
            {title === "自动审核可否判定"
              ? "根据征信结果和风控规则判断是否需要人工审核"
              : title === "贷款额度自动审核"
                ? "根据基础公式进行额度计算评估，判定申请者额度是否通过"
                : ""}
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              {title === "自动审核可否判定" ? (
                <>
                  <h4 className="text-md font-semibold mb-2">判断逻辑</h4>
                  <div className="p-2 border-r flex items-center justify-start">
                    信用标准分是可修改参数
                  </div>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-start">
                        1.申请者信用评分小于征信标准分触发人工审核
                      </div>
                    </div>
                    <div className="grid border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-start">
                        2.近 2 年逾期次数大于3 次触发人工审核
                      </div>
                    </div>
                    <div className="grid border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-start">
                        3.以外判定为自动审核
                      </div>
                    </div>
                  </div>
                  {/* <h4 className="text-md font-semibold mt-4 mb-2">输出参数</h4>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid grid-cols-2 border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-center">
                        信用评分
                      </div>
                      <div className="p-2 flex items-center justify-center">
                        600 分以下为高风险
                      </div>
                    </div>
                    <div className="grid grid-cols-2 border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-center">
                        逾期记录
                      </div>
                      <div className="p-2 flex items-center justify-center">
                        若 &gt;3 次需触发人工审核
                      </div>
                    </div>
                    <div className="grid grid-cols-2 border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-center">
                        负债率
                      </div>
                      <div className="p-2 flex items-center justify-center">
                        总负债/年收入
                      </div>
                    </div>
                  </div> */}
                </>
              ) : title === "贷款额度自动审核" ? (
                <>
                  <h4 className="text-md font-semibold mb-2">判断逻辑</h4>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-start">
                        1.申请额度小于等于最大贷款额度则审核通过
                      </div>
                    </div>
                    <div className="grid border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-start">
                        2.申请额度大于最大贷款额度则审核不通过
                      </div>
                    </div>
                  </div>
                  {/* <h4 className="text-md font-semibold mt-4 mb-2">
                    可修改参数
                  </h4>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid grid-cols-2 border-b last:border-b-0">
                      <div className="p-2 border-r flex items-center justify-center">
                        最大贷款额度 30 万元
                      </div>
                      <div className="p-2 flex items-center justify-center"></div>
                    </div>
                  </div> */}
                </>
              ) : null}
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

const DecisionNode = memo((props: DecisionNodeProps) => {
  const nodeData = props.data as NodeData;
  const { id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [isExecuting, setIsExecuting] = useState(false);
  const [showLoanAmount, setShowLoanAmount] = useState(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 监听节点状态变化，控制贷款金额的显示
  useEffect(() => {
    if (nodeData.title === "贷款额度自动审核") {
      setShowLoanAmount(true);
    }
  }, [nodeData.title]);

  // 执行节点的函数
  const executeNode = useCallback(async () => {
    if (isExecuting) return;

    setIsExecuting(true);

    try {
      // 直接从画面上获取征信标准分输入框的值
      const creditCheckScoreInput = document.querySelector(
        'input[placeholder="请输入征信标准分"]'
      ) as HTMLInputElement;
      const creditCheckScore = creditCheckScoreInput
        ? parseInt(creditCheckScoreInput.value) || 600
        : 600;

      await executeNodeWithResult(
        id as string,
        null,
        "decisionNode",
        setNodes,
        getNodes,
        getEdges,
        creditCheckScore
      );
    } catch (error) {
      console.error("执行节点出错:", error);
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...(node.data as NodeData),
                status: "error",
              },
            };
          }
          return node;
        })
      );
    } finally {
      setIsExecuting(false);
    }
  }, [id, isExecuting, setNodes, getNodes, getEdges]);

  // 监听父组件传入的 status
  useEffect(() => {
    if (nodeData.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [nodeData.status, isExecuting, executeNode]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    if (isExecuting) return;

    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...(node.data as NodeData),
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode
        {...props}
        data={nodeData as Record<string, unknown>}
        id={id as string}
      >
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="target"
        />
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{nodeData.title ?? "决策节点"}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <div className="p-4">
          <div className="text-sm text-muted-foreground">
            {nodeData.condition === "征信查询判定" && "征信查询判定"}
            {nodeData.condition === "额度审核判定" && "额度审核判定"}
          </div>
        </div>
        {nodeData.title === "自动审核可否判定" && (
          <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
            <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
              <span className="text-sm font-medium">
                征信标准分：
                {(() => {
                  const input = document.querySelector(
                    'input[placeholder="请输入征信标准分"]'
                  ) as HTMLInputElement;
                  return input ? input.value : "600";
                })()}
              </span>
            </div>
          </div>
        )}
        {nodeData.title === "贷款额度自动审核" && (
          <>
            <div className="absolute -left-24 top-1/2 transform -translate-y-1/2">
              <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
                <span className="text-sm font-medium">
                  入力贷款金额：
                  {showLoanAmount ? nodeData.loanAmount || "0" : "--"}
                </span>
              </div>
            </div>
            <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
              <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
                <span className="text-sm font-medium">
                  最大贷款额度：{nodeData.maxAmount || "0"}万
                </span>
              </div>
            </div>
          </>
        )}
        <div className="flex justify-between mt-4">
          <Handle
            type="source"
            position={Position.Bottom}
            id="true"
            className="!bg-green-500 !w-3 !h-3"
            style={{ left: "25%" }}
          />
          <Handle
            type="source"
            position={Position.Bottom}
            id="false"
            className="!bg-red-500 !w-3 !h-3"
            style={{ left: "75%" }}
          />
        </div>
        <DecisionNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          title={nodeData.title ?? "决策节点"}
        />
      </BaseNode>
    </div>
  );

  if (
    nodeData.status === "success" ||
    nodeData.status === "error" ||
    nodeData.status === "loading"
  ) {
    return (
      <NodeStatusIndicator status={nodeData.status}>
        {content}
      </NodeStatusIndicator>
    );
  }
  return content;
});

DecisionNode.displayName = "DecisionNode";

export default DecisionNode;
