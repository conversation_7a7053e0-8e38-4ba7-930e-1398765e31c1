import { memo, useState, useEffect } from "react";
import { <PERSON>le, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { executeNodeWithResult } from "@/src/utils/node-executor";
import { toast } from "sonner";
// import { TEST_CASES } from "@/src/config/test-data";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";

interface AutoApprovalNodeData {
  title: string;
  status?: "success" | "loading" | "error";
  maxAmount?: string;
  inputValue?: string;
  loanAmount?: string;
}

type AutoApprovalNodeProps = NodeProps<AutoApprovalNodeData>;

// 从测试数据中获取信用评分
const fetchMaxAmount = async (inputValue: string): Promise<number> => {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 从测试数据中获取信用评分
  const testCase = TEST_CASES[inputValue];
  if (!testCase) {
    throw new Error("未找到对应的测试数据");
  }

  return testCase.maxAmount;
};

// 新增 AutoApprovalNodeDrawer 组件
function AutoApprovalNodeDrawer({
  isOpen,
  onOpenChange,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>自动审核节点说明</DrawerTitle>
          <DrawerDescription>
            该节点用于自动审核用户的贷款额度
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <p className="text-sm text-muted-foreground">
                1. 根据用户ID查询对应的最大可贷额度
              </p>
              <p className="text-sm text-muted-foreground">
                2. 将用户申请的贷款金额与最大可贷额度进行比对
              </p>
              <p className="text-sm text-muted-foreground">
                3. 如果申请金额超过最大可贷额度，则审核不通过
              </p>
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

const AutoApprovalNode = memo((props: AutoApprovalNodeProps) => {
  const { data, id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [status, setStatus] = useState<
    "success" | "loading" | "error" | undefined
  >(undefined);
  const [isExecuting, setIsExecuting] = useState(false);
  const [maxAmount, setMaxAmount] = useState<number | undefined>(
    data.maxAmount
  );
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [showLoanAmount, setShowLoanAmount] = useState(false);

  // 执行节点的函数
  const executeNode = async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    setStatus("loading");
    setShowLoanAmount(false); // 重置入力金额显示状态

    try {
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 调用 API 获取信用评分
      const maxAmount = await fetchMaxAmount(data.inputValue as string);
      setMaxAmount(maxAmount);
      setStatus("success");

      // 使用公共执行方法处理节点逻辑
      await executeNodeWithResult(
        id,
        maxAmount,
        "autoApprovalNode",
        setNodes,
        getNodes,
        getEdges
      );

      // 延迟显示入力金额
      setTimeout(() => {
        setShowLoanAmount(true);
      }, 1);

      // 清除当前节点的状态
      setStatus(undefined);
      setIsExecuting(false);
    } catch (error) {
      setStatus("error");
      toast.error("自动审核失败");
      setIsExecuting(false);
    }
  };

  // 监听父组件传入的 status
  useEffect(() => {
    if (data.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [data.status]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode {...props} data={data as Record<string, unknown>}>
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="target"
        />
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <AutoApprovalNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
        />
        <div>
          <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
            <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
              <span className="text-sm font-medium">
                最大额度：{maxAmount}万
              </span>
            </div>
          </div>
          <div className="absolute -left-24 top-1/2 transform -translate-y-1/2">
            <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
              <span className="text-sm font-medium">
                入力金额：{showLoanAmount ? data.loanAmount : ""}
              </span>
            </div>
          </div>
        </div>
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="source"
        />
      </BaseNode>
    </div>
  );

  if (status) {
    return <NodeStatusIndicator status={status}>{content}</NodeStatusIndicator>;
  }
  return content;
});

AutoApprovalNode.displayName = "AutoApprovalNode";

export default AutoApprovalNode;
