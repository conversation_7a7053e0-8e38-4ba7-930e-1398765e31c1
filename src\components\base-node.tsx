import { forwardRef, ReactNode } from "react";
import { cn } from "@/lib/utils";
import { NodeProps } from "@xyflow/react";

interface BaseNodeProps<T = Record<string, unknown>> {
  className?: string;
  children: ReactNode;
  data: T;
  id: string;
  selected?: boolean;
}

const BaseNode = forwardRef<HTMLDivElement, BaseNodeProps>(
  ({ className, selected, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative rounded-md border bg-card p-5 text-card-foreground",
          className,
          selected ? "border-muted-foreground shadow-lg" : "",
          "hover:ring-1",
        )}
        tabIndex={0}
      >
        {children}
      </div>
    );
  }
);

BaseNode.displayName = "BaseNode";

export default BaseNode;
