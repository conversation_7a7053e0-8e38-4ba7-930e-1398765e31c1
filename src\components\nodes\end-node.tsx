import { memo, useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { toast } from "sonner";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";
import { executeNodeWithResult, NodeData } from "@/src/utils/node-executor";

// 节点数据接口
interface EndNodeData extends Record<string, unknown> {
  title: string;
  type: string;
  status?: "success" | "loading" | "error";
  order: number;
  message?: string;
  inputValue?: string;
  loanAmount?: string;
  isBlacklist?: boolean;
}

// 结束节点配置抽屉组件
function EndNodeDrawer({
  isOpen,
  onOpenChange,
  data,
  onMessageChange,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  data: EndNodeData;
  onMessageChange: (message: string) => void;
}) {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>结束节点配置</DrawerTitle>
          <DrawerDescription>配置结束节点的提示信息</DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="message">提示信息</Label>
              <Input
                id="message"
                value={data.message || ""}
                onChange={(e) => onMessageChange(e.target.value)}
                placeholder="请输入结束节点的提示信息"
              />
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

type EndNodeProps = NodeProps<NodeData>;

const EndNode = memo((props: EndNodeProps) => {
  const { data, id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [status, setStatus] = useState<"success" | "loading" | "error" | undefined>(undefined);
  const [isExecuting, setIsExecuting] = useState(false);
  const [message, setMessage] = useState(data.message || "流程执行完成");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 执行节点的函数
  const executeNode = async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    setStatus("loading");

    // 模拟 API 调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setStatus("success");

    // 使用公共执行方法处理节点逻辑
    await executeNodeWithResult(
      id,
      true,
      'endNode',
      setNodes,
      getNodes,
      getEdges
    );

    // 显示成功提示
    toast.success(message);

    // 清除当前节点的状态
    setStatus(undefined);
    setIsExecuting(false);
  };

  // 监听父组件传入的 status
  useEffect(() => {
    if (data.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [data.status]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleMessageChange = (newMessage: string) => {
    setMessage(newMessage);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              message: newMessage,
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode {...props} data={data as Record<string, unknown>} id={id}>
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="target"
        />
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <div className="p-4">
          <div className="text-sm text-muted-foreground">{message}</div>
        </div>
        <EndNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          data={data}
          onMessageChange={handleMessageChange}
        />
      </BaseNode>
    </div>
  );

  if (status) {
    return <NodeStatusIndicator status={status}>{content}</NodeStatusIndicator>;
  }
  return content;
});

EndNode.displayName = "EndNode";

export default EndNode;