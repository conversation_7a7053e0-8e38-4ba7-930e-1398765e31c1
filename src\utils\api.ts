// FILEPATH: c:/Users/<USER>/vscodeWorkSpace/FrontEnd/src/utils/api.ts

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

// 创建一个axios实例
const apiClient = axios.create({
  baseURL: 'http://9.112.150.243:8080/api', // 替换为你的API基础URL
  timeout: 10000, // 请求超时时间
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 在发送请求之前做些什么
    console.log('🚀 API请求:', {
      url: config.url,
      method: config.method,
      data: config.data,
      headers: config.headers
    });
    // 比如添加token
    // config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    console.error('❌ API请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    console.log('✅ API响应:', {
      url: response.config.url,
      method: response.config.method,
      status: response.status,
      data: response.data,
      requestData: response.config.data
    });
    return response;
  },
  (error) => {
    // 对响应错误做点什么
    console.error('❌ API响应错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data,
      requestData: error.config?.data
    });
    return Promise.reject(error);
  }
);

// 封装通用请求方法
export const request = async <T = any>(config: AxiosRequestConfig): Promise<T> => {
  console.log('📡 开始API调用:', config);
  try {
    const response = await apiClient.request<T>(config);
    console.log('📡 API调用成功，返回数据:', response.data);
    return response.data;
  } catch (error) {
    console.error('📡 API调用失败:', error);
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 500) {
        console.error('服务器内部错误，请检查后端服务');
      }
    }
    throw error;
  }
};