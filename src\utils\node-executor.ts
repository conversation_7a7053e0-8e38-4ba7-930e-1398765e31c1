import { Node, useReactFlow } from "@xyflow/react";
import { toast } from "sonner";
import { request } from '@/src/utils/api';
export interface NodeData extends Record<string, unknown> {
  title: string;
  type: string;
  status?: 'success' | 'loading' | 'error';
  order: number;
  inputValue?: string;
  loanAmount?: string;
  monthlyIncome?: string;
  occupation?: string;
  condition?: string;
  conditionType?: string;
  score?: number;
  approvalResult?: boolean;
  message?: string;
  isBlacklist?: boolean;
  //creditCheckScore?: number;
  maxAmount?: number;
  debt_ratio?: number;
  overdue_records?: number;
  loanableAmount?: number;
  token?: string;
  name?: string;
  auditResult?: number;
  routeDecision?: 'approve' | 'manual' | 'reject';
}

export type CustomNode = Node<NodeData>;

export const executeNodeWithResult = async (
  nodeId: string,
  result: any,
  nodeType: string,
  setNodes: ReturnType<typeof useReactFlow>['setNodes'],
  getNodes: ReturnType<typeof useReactFlow>['getNodes'],
  getEdges: ReturnType<typeof useReactFlow>['getEdges'],
  creditCheckScore: number,
  debtRatio?: number,
  expectedDifference?: number
) => {
  // 获取当前节点
  const currentNode = getNodes().find(n => n.id === nodeId);
  if (!currentNode) return;

  const nodeData = currentNode.data as unknown as NodeData;
  const inputValue = nodeData.inputValue;
  const creditScore = nodeData.score;

  // 格式化贷款金额（添加"万"单位）
  const checkAmount = nodeData.loanAmount ? parseInt(nodeData.loanAmount) : 0;
  const formattedLoanAmount = nodeData.loanAmount?.includes("万")
    ? nodeData.loanAmount
    : nodeData.loanAmount ? `${nodeData.loanAmount}万` : undefined;

  // 根据节点类型判断是否继续流程
  let isTrue = false;

  switch (nodeType) {
    case 'startNode':
      // 对于用户信息采集智导节点，只需要收集信息，不调用API
      if (nodeData.name === 'userInfoCollection') {
        console.log('用户信息采集智导 - 收集用户信息');
        isTrue = true;
      } else {
        isTrue = true;
      }
      break;
    case 'creditCheckNode':
      // 根据节点名称调用不同的API
      if (nodeData.name === 'riskPreCheck') {
        // 风控预审智导 - 调用新的API
        try {
          // 获取用户信息采集智导节点的数据
          const userInfoNode = getNodes().find(n =>
            (n.data as NodeData).name === 'userInfoCollection'
          );
          const userInfoData = userInfoNode?.data as NodeData;

          const requestData = {
            method: 'POST',
            url: '/events/system/createInstanceV1',
            data: {
              name: '风控预审智导',
              inputParams: {
                key1: userInfoData?.inputValue || inputValue || '310XXXXXXXXX',
                key2: userInfoData?.loanAmount || '10000',
                key3: userInfoData?.occupation || 'IT'
              },
            },
          };
          console.log('风控预审智导请求:', requestData);

          const response = await request<{
            data: {
              'riskManagementApiSession.creditScore': string,
              'riskManagementApiSession.debtRatio': string,
              'riskManagementApiSession.loanableAmount': string
            },
            token: string
          }>(requestData);

          // 解析响应数据
          const creditScore = parseInt(response.data['riskManagementApiSession.creditScore']) || 600;
          const debtRatio = parseInt(response.data['riskManagementApiSession.debtRatio']) || 60;
          const loanableAmount = parseInt(response.data['riskManagementApiSession.loanableAmount']) || 100000;

          isTrue = true;
          // 保存风控预审数据到节点中
          setNodes((nodes) =>
            nodes.map((node) => {
              if (node.id === nodeId) {
                return {
                  ...node,
                  data: {
                    ...node.data,
                    score: creditScore,
                    debt_ratio: debtRatio,
                    loanableAmount: loanableAmount,
                    token: response.token,
                  },
                };
              }
              return node;
            })
          );

          console.log('风控预审智导结果:', {
            creditScore,
            debtRatio,
            loanableAmount,
            token: response.token
          });
        } catch (error) {
          console.error('风控预审智导失败:', error);
          isTrue = false;
        }
      } else if (nodeData.name === 'productMatching') {
        // 贷款产品匹配智导 - 模拟处理
        console.log('贷款产品匹配智导 - 模拟执行');
        isTrue = true;
        // 模拟处理延时
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else if (nodeData.name === 'documentOCR') {
        // 材料上传与OCR智导 - 模拟处理
        console.log('材料上传与OCR智导 - 模拟执行');
        isTrue = true;
        // 模拟处理延时
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else if (nodeData.name === 'contractGeneration') {
        // 合同生成与签署智导 - 模拟处理
        console.log('合同生成与签署智导 - 模拟执行');
        isTrue = true;
        // 模拟处理延时
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        // 原有的征信查询逻辑
        try {
          const requestData = {
            method: 'POST',
            url: '/events/system/createInstance',
            data: {
              name: '征信查询',
              inputParams: [inputValue],
            },
          };
          console.log('征信查询请求:', requestData);
          const response = await request<{ code: string, msg: string, data: { credit_score: number, overdue_records: number, debt_ratio: number } }>(requestData);
          const currentScore = response.data?.credit_score;
          if (currentScore === undefined || currentScore === null) {
            isTrue = false;
          } else {
            isTrue = true;
            // 保存所有征信数据到节点中
            setNodes((nodes) =>
              nodes.map((node) => {
                if (node.id === nodeId) {
                  return {
                    ...node,
                    data: {
                      ...node.data,
                      score: response.data.credit_score,
                      debt_ratio: response.data.debt_ratio,
                      overdue_records: response.data.overdue_records,
                    },
                  };
                }
                return node;
              })
            );
          }
          console.log('征信判断:', { response });
        } catch (error) {
          console.error('征信查询失败:', error);
          isTrue = false;
        }
      }
      break;
    case 'decisionNode':
      if (nodeData.condition === "自动审核可否判定") {
        try {
          const requestData = {
            method: 'POST',
            url: '/events/system/createInstance',
            data: {
              name: '自动审核可否判定',
              inputParams: [nodeData.score?.toString() ?? '0', creditCheckScore.toString()],
            },
          };
          console.log('自动审核可否判定请求:', requestData);
          const response = await request<{
            code: string,
            msg: string,
            data: {
              automati_review: boolean
            }
          }>(requestData);
          // console.log('自动审核判定结果:', { response. });
          isTrue = response.data?.automati_review ?? false;
          console.log('自动审核判定结果:', { score: nodeData.score, creditCheckScore, isTrue });
        } catch (error) {
          console.error('自动审核判定失败:', error);
          isTrue = false;
        }
      } else if (nodeData.condition === "额度审核判定") {
        try {
          // 获取当前节点的完整数据
          const currentNode = getNodes().find(n => n.id === nodeId);
          const currentData = currentNode?.data as NodeData;

          // 从征信查询节点获取数据
          const creditCheckNode = getNodes().find(n =>
            (n.data as NodeData).type === 'creditCheckNode' &&
            (n.data as NodeData).status === 'success'
          );
          const creditCheckData = creditCheckNode?.data as NodeData;

          const requestData = {
            method: 'POST',
            url: '/events/system/createInstance',
            data: {
              name: '贷款额度自动审核',
              inputParams: [
                inputValue,
                currentData.score?.toString() ?? '0',
                creditCheckData?.debt_ratio?.toString() ?? '0',
                creditCheckData?.overdue_records?.toString() ?? '0'
              ],
            },
          };
          console.log('额度审核判定请求:', requestData);
          const response = await request<{
            code: string,
            msg: string,
            data: {
              approval_limit: number
            }
          }>(requestData);

          const maxAllowedAmount = response.data?.approval_limit ?? 0;
          const currentAmount = checkAmount ?? 0;
          isTrue = maxAllowedAmount >= currentAmount;

          // 保存最大额度到节点数据中
          setNodes((nodes) =>
            nodes.map((node) => {
              if (node.id === nodeId) {
                return {
                  ...node,
                  data: {
                    ...node.data,
                    maxAmount: maxAllowedAmount,
                    status: "success",
                    debt_ratio: creditCheckData?.debt_ratio,
                    overdue_records: creditCheckData?.overdue_records
                  },
                };
              }
              return node;
            })
          );

          // 更新当前节点的数据
          nodeData.maxAmount = maxAllowedAmount;
          nodeData.debt_ratio = creditCheckData?.debt_ratio;
          nodeData.overdue_records = creditCheckData?.overdue_records;

          console.log('额度审核判定结果:', {
            currentAmount,
            maxAllowedAmount,
            isTrue,
            inputValue,
            score: currentData.score,
            debt_ratio: creditCheckData?.debt_ratio,
            overdue_records: creditCheckData?.overdue_records
          });
        } catch (error) {
          console.error('额度审核判定失败:', error);
          isTrue = false;
        }
      }
      break;
    case 'threeWayDecisionNode':
      // 决策器逻辑
      try {
        // 获取风控预审智导节点的数据
        const riskPreCheckNode = getNodes().find(n =>
          (n.data as NodeData).name === 'riskPreCheck'
        );
        const riskPreCheckData = riskPreCheckNode?.data as NodeData;

        // 获取用户信息采集智导节点的数据
        const userInfoNode = getNodes().find(n =>
          (n.data as NodeData).name === 'userInfoCollection'
        );
        const userInfoData = userInfoNode?.data as NodeData;

        const requestData = {
          method: 'POST',
          url: '/events/system/createInstanceV1',
          data: {
            name: '决策器',
            inputParams: {
              creditScore: (riskPreCheckData?.score || 700).toString(),
              debtRatio: ((riskPreCheckData?.debt_ratio || 55) / 100).toString(), // 转换为小数字符串
              applyAmount: (userInfoData?.loanAmount || '100').toString(),
              loanableAmount: (riskPreCheckData?.loanableAmount || 120000).toString()
            },
            visParams: {
              standardScore: (creditCheckScore || 600).toString(),
              standardDebtRatio: ((debtRatio || 65) / 100).toString(), // 转换为小数字符串
              expectedDiff: (expectedDifference || 30).toString()
            }
          },
        };
        console.log('决策器请求:', requestData);

        const response = await request<{
          data: {
            'splitterApiSession.auditResult': number
          },
          token: string
        }>(requestData);

        // 解析审核结果：0-自动审核(approve), 1-人工(manual), 2-拒绝(reject)
        const auditResult = response.data['splitterApiSession.auditResult'];
        let routeDecision: 'approve' | 'manual' | 'reject';

        switch (auditResult) {
          case 0:
            routeDecision = 'approve'; // 自动审核
            break;
          case 1:
            routeDecision = 'manual'; // 人工
            break;
          case 2:
            routeDecision = 'reject'; // 拒绝
            break;
          default:
            routeDecision = 'manual';
        }

        console.log('决策器结果:', {
          auditResult,
          routeDecision,
          token: response.token,
          inputParams: requestData.data.inputParams,
          visParams: requestData.data.visParams
        });

        // 设置分流结果到节点数据
        setNodes((nodes) =>
          nodes.map((node) => {
            if (node.id === nodeId) {
              return {
                ...node,
                data: {
                  ...node.data,
                  routeDecision: routeDecision,
                  auditResult: auditResult,
                  status: "success",
                  debtRatio: debtRatio,
                  expectedDifference: expectedDifference,
                  token: response.token,
                },
              };
            }
            return node;
          })
        );

        // 根据分流结果设置 isTrue 和路由信息
        nodeData.routeDecision = routeDecision;
        isTrue = true; // 三路分流总是成功，具体路由在后面处理
      } catch (error) {
        console.error('决策器失败:', error);
        isTrue = false;
      }
      break;
    case 'endNode':
      isTrue = false;
      break;
    default:
      isTrue = true;
  }

  // 显示执行结果提示
  const resultMessage = nodeType === 'creditCheckNode'
    ? `信用评分查询完成: ${result}`
    : nodeType === 'endNode'
      ? nodeData.message || "流程执行完成"
      : nodeType === 'startNode'
        ? nodeData.name === 'userInfoCollection'
          ? `用户信息采集完成：用户ID ${inputValue}, 贷款金额 ${formattedLoanAmount}`
          : `申请审核开始：贷款金额 ${formattedLoanAmount}`
        : nodeType === 'threeWayDecisionNode'
          ? `决策器执行完成: ${nodeData.routeDecision === 'approve' ? '自动审核' : nodeData.routeDecision === 'manual' ? '人工审核' : '拒绝'}`
          : nodeType === 'decisionNode'
            ? ` (${nodeData.condition === "自动审核可否判定"
              ? `当前分数: ${nodeData.score}, 要求分数: ${creditCheckScore}`
              : `当前额度: ${formattedLoanAmount}, 最大额度: ${nodeData.maxAmount}万`})`
            : `审核完成: ${result ? '通过' : '不通过'}`;
  toast.success(resultMessage);

  // 等待动画效果完成
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 更新当前节点状态
  setNodes((nodes) =>
    nodes.map((node) => {
      if (node.id === nodeId) {
        const updatedData = {
          ...node.data as NodeData,
          status: "success",
          creditCheckScore: creditCheckScore,
          score: creditScore,
          ...(nodeType === 'creditCheckNode' ? { score: result as number } : {}),
        } as NodeData;

        // 如果是额度审核节点，确保maxAmount被保留
        if (nodeType === 'decisionNode' && nodeData.condition === "额度审核判定") {
          updatedData.maxAmount = nodeData.maxAmount;
          console.log('更新节点数据:', { maxAmount: nodeData.maxAmount });
        }

        return {
          ...node,
          data: updatedData,
        };
      }
      return node;
    })
  );

  // 等待状态更新完成
  await new Promise(resolve => setTimeout(resolve, 500));

  // 查找对应的边
  let edges;
  if (nodeType === 'threeWayDecisionNode') {
    // 三路分流节点根据路由决策选择边
    // 从当前节点数据中获取最新的路由决策
    const currentNode = getNodes().find(n => n.id === nodeId);
    const currentNodeData = currentNode?.data as NodeData;
    const routeDecision = currentNodeData?.routeDecision || nodeData.routeDecision || 'manual';

    console.log('三路分流边查找:', {
      nodeId,
      routeDecision,
      currentNodeData: currentNodeData?.routeDecision,
      nodeDataRoute: nodeData.routeDecision
    });

    edges = getEdges().filter(
      (e) => e.source === nodeId && e.sourceHandle === routeDecision
    );

    console.log('找到的边:', edges.map(e => ({ id: e.id, sourceHandle: e.sourceHandle, target: e.target })));
  } else {
    // 对于模拟节点（productMatching, documentOCR, contractGeneration），查找所有从该节点出发的边
    if (nodeData.name === 'productMatching' || nodeData.name === 'documentOCR' || nodeData.name === 'contractGeneration') {
      edges = getEdges().filter((e) => e.source === nodeId);
      console.log(`${nodeData.name} 查找边:`, edges.map(e => ({ id: e.id, target: e.target })));
    } else {
      edges = getEdges().filter(
        (e) => e.source === nodeId &&
          (nodeType === 'decisionNode' ? e.sourceHandle === (isTrue ? "true" : "false") : e.sourceHandle === "true")
      );
    }
  }

  // 如果找到边，则触发目标节点
  if (edges.length > 0) {
    const targetNodeId = edges[0].target;
    // 等待一段时间后再触发下一个节点
    await new Promise(resolve => setTimeout(resolve, 500));

    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === targetNodeId) {
          const updatedData = {
            ...node.data as NodeData,
            status: "loading",
            inputValue: inputValue,
            loanAmount: formattedLoanAmount,
            creditCheckScore: creditCheckScore,
            score: creditScore,
            ...(nodeType === 'creditCheckNode' ? { score: result as number } : {}),
          } as NodeData;

          // 如果是额度审核节点，确保maxAmount被保留
          if (nodeType === 'decisionNode' && nodeData.condition === "额度审核判定") {
            updatedData.maxAmount = nodeData.maxAmount;
          }

          return {
            ...node,
            data: updatedData,
          };
        }
        return node;
      })
    );
  } else if (isTrue && nodeType !== 'endNode') {
    const nextNode = getNodes().find(n =>
      (n.data as unknown as NodeData).order === (nodeData.order ?? 0) + 1
    );

    if (nextNode) {
      // 等待一段时间后再触发下一个节点
      await new Promise(resolve => setTimeout(resolve, 500));

      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === nextNode.id) {
            return {
              ...node,
              data: {
                ...node.data as NodeData,
                status: "loading",
                inputValue: inputValue,
                loanAmount: formattedLoanAmount,
                creditCheckScore: creditCheckScore,
                score: creditScore,
                maxAmount: nodeData.maxAmount,
                ...(nodeType === 'creditCheckNode' ? { score: result as number } : {}),
              } as NodeData,
            };
          }
          return node;
        })
      );
    }
  }
};