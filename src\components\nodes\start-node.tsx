import { memo, useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";
import { executeNodeWithResult, NodeData } from "@/src/utils/node-executor";

type StartNodeProps = NodeProps<NodeData>;

// 新增 StartNodeDrawer 组件
function StartNodeDrawer({
  isOpen,
  onOpenChange,
  inputValue,
  onInputChange,
  loanAmount,
  onLoanAmountChange,
  monthlyIncome,
  onMonthlyIncomeChange,
  occupation,
  onOccupationChange,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  inputValue: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  loanAmount: string;
  onLoanAmountChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  monthlyIncome: string;
  onMonthlyIncomeChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  occupation: string;
  onOccupationChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>用户信息采集智导(过滤器)</DrawerTitle>
          <DrawerDescription>
            收集用户身份,年龄,职业,收入等基础信息;贷款意向(贷款金额,期限)
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="id">请输入以下信息</Label>
              <Label htmlFor="id">请输入6位ID</Label>
              <Input
                id="id"
                value={inputValue}
                onChange={onInputChange}
                placeholder="请输入6位数字ID"
                maxLength={6}
              />
              <p className="text-sm text-muted-foreground">
                唯一用户ID,条件节点将根据ID尾号判断流程走向
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="loanAmount">贷款金额</Label>
              <Input
                id="loanAmount"
                value={loanAmount}
                onChange={onLoanAmountChange}
                placeholder="请输入贷款金额"
                type="number"
              />
              <p className="text-sm text-muted-foreground">
                以万元为单位,用户后续额度审核
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="monthlyIncome">月收入</Label>
              <Input
                id="monthlyIncome"
                value={monthlyIncome}
                onChange={onMonthlyIncomeChange}
                placeholder="请输入月收入(以元为单位)"
                type="number"
              />
              <p className="text-sm text-muted-foreground">
                以元为单位,用于后续额度审核
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="occupation">职业</Label>
              <Input
                id="occupation"
                value={occupation}
                onChange={onOccupationChange}
                placeholder="请输入职业"
              />
              <p className="text-sm text-muted-foreground">
                例:服务业,金融业,技术业,自由职业等
              </p>
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
          <Button onClick={() => onOpenChange(false)}>确认</Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

const StartNode = memo((props: StartNodeProps) => {
  const { data, id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [status, setStatus] = useState<
    "success" | "loading" | "error" | undefined
  >(undefined);
  const [isExecuting, setIsExecuting] = useState(false);
  const [inputValue, setInputValue] = useState((data as any).inputValue || "");
  const [loanAmount, setLoanAmount] = useState((data as any).loanAmount || "");
  const [monthlyIncome, setMonthlyIncome] = useState(
    (data as any).monthlyIncome || ""
  );
  const [occupation, setOccupation] = useState((data as any).occupation || "");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 执行节点的函数
  const executeNode = async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    setStatus("loading");

    // 模拟 API 调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 设置成功状态
    setStatus("success");

    // 使用公共执行方法处理节点逻辑
    await executeNodeWithResult(
      id,
      true,
      "startNode",
      setNodes,
      getNodes,
      getEdges,
      600 // 默认征信分数，对于用户信息采集节点不会用到
    );

    // 清除当前节点的状态
    setStatus(undefined);
    setIsExecuting(false);
  };

  // 监听父组件传入的 status
  useEffect(() => {
    if (data.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [data.status]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/[^\d]/g, "").slice(0, 6); // 只允许数字，最多6位
    setInputValue(newValue);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              inputValue: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  const handleLoanAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLoanAmount(newValue);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              loanAmount: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  const handleMonthlyIncomeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValue = e.target.value;
    setMonthlyIncome(newValue);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              monthlyIncome: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  const handleOccupationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setOccupation(newValue);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              occupation: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode
        {...props}
        data={data as unknown as Record<string, unknown>}
        id={id}
      >
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <StartNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          inputValue={inputValue}
          onInputChange={handleInputChange}
          loanAmount={loanAmount}
          onLoanAmountChange={handleLoanAmountChange}
          monthlyIncome={monthlyIncome}
          onMonthlyIncomeChange={handleMonthlyIncomeChange}
          occupation={occupation}
          onOccupationChange={handleOccupationChange}
        />
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="source"
        />
      </BaseNode>
    </div>
  );

  if (status) {
    return <NodeStatusIndicator status={status}>{content}</NodeStatusIndicator>;
  }
  return content;
});

StartNode.displayName = "StartNode";

export default StartNode;
