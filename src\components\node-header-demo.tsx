import { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "./base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";

interface CustomNodeData extends Record<string, unknown> {
    title: string;
    type: string;
}

const NodeHeaderDemo = memo((props: NodeProps<CustomNodeData>) => {
    const { data, id } = props;
    const { setNodes, setEdges } = useReactFlow();

    const onDelete = () => {
        setNodes((nodes) => nodes.filter((node) => node.id !== id));
        setEdges((edges) => edges.filter((edge) => edge.source !== id && edge.target !== id));
    };

    return (
        <BaseNode {...props}>
            <Handle
                type="target"
                position={Position.Top}
                className="w-3 h-3 bg-primary"
                isConnectable={true}
                id="target"
            />
            <NodeHeader>
                <div className="flex items-center justify-between">
                    <span>{data.title}</span>
                    <div className="flex gap-1">
                        <button className="p-1 hover:bg-accent rounded-md">
                            <Play className="w-4 h-4" />
                        </button>
                        <button
                            className="p-1 hover:bg-accent rounded-md"
                            onClick={onDelete}
                        >
                            <Trash2 className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </NodeHeader>
            <Handle
                type="source"
                position={Position.Bottom}
                className="w-3 h-3 bg-primary"
                isConnectable={true}
                id="source"
            />
        </BaseNode>
    );
});

NodeHeaderDemo.displayName = "NodeHeaderDemo";

export default NodeHeaderDemo; 