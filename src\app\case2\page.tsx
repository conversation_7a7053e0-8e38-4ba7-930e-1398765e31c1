"use client";

import "@xyflow/react/dist/style.css";
import {
  Background,
  React<PERSON>low,
  Node,
  useReact<PERSON>low,
  ReactFlowProvider,
  NodeChange,
  applyNodeChanges,
  Edge,
  EdgeChange,
  applyEdgeChanges,
  addEdge,
  Connection,
  ConnectionMode,
  NodeTypes,
} from "@xyflow/react";
import Node<PERSON>eaderDemo from "@/src/components/node-header-demo";
import NodeWithStatus from "@/src/components/node-with-status";
import ButtonEdge from "@/src/components/button-edge";
import { Sidebar } from "@/src/components/Sidebar";
import { useState, useCallback, useEffect } from "react";
import StartNode from "@/src/components/nodes/start-node";
import DecisionNode from "@/src/components/nodes/decision-node";
import ThreeWayDecisionNode from "@/src/components/nodes/three-way-decision-node";
import EndNode from "@/src/components/nodes/end-node";
import CreditCheckNode from "@/src/components/nodes/credit-check-node";
import AutoA<PERSON>rovalNode from "@/src/components/nodes/auto-approval-node";
import { Label } from "@/src/components/ui/label";
import { Input } from "@/src/components/ui/input";
import { Button } from "@/src/components/ui/button";

interface CustomNodeData extends Record<string, unknown> {
  title: string;
  type: string;
  status?: "success" | "loading" | "error";
  order: number;
  name?: string;
  condition?: string;
  inputLabel?: string;
  score?: number;
  validateInput?: (input: string) => { valid: boolean; message: string };
  attemptCount?: number;
  creditCheckScore?: number;
  [key: string]: unknown;
}

const nodeTypes = {
  nodeHeaderNode: NodeHeaderDemo,
  nodeWithStatus: NodeWithStatus,
  startNode: StartNode,
  decisionNode: DecisionNode,
  threeWayDecisionNode: ThreeWayDecisionNode,
  endNode: EndNode,
  creditCheckNode: CreditCheckNode,
  autoApprovalNode: AutoApprovalNode,
} as NodeTypes;

const edgeTypes = {
  buttonedge: ButtonEdge,
};

const initialNodes: Node<CustomNodeData>[] = [
  {
    id: "1",
    type: "startNode",
    position: { x: 400, y: 50 },
    data: {
      title: "用户信息采集智导",
      type: "startNode",
      order: 1,
      name: "userInfoCollection",
      inputLabel: "请输入用户ID",
      validateInput: (input: string) => {
        if (!/^\d{6}$/.test(input)) {
          return { valid: false, message: "请输入6位数字ID" };
        }
        return { valid: true, message: "" };
      },
    },
  },
  {
    id: "2",
    type: "creditCheckNode",
    position: { x: 400, y: 200 },
    data: {
      title: "风控预审智导(连接器)",
      type: "creditCheckNode",
      order: 2,
      condition: "风控预审结果",
      name: "riskPreCheck",
    },
  },
  {
    id: "3",
    type: "threeWayDecisionNode",
    position: { x: 400, y: 350 },
    data: {
      title: "决策器",
      type: "threeWayDecisionNode",
      order: 3,
      condition: "预审结果分流",
      name: "routingDecision",
      attemptCount: 0,
    },
  },
  // 分支A：通过预审 → 进入贷款产品匹配
  {
    id: "4",
    type: "creditCheckNode",
    position: { x: 100, y: 500 },
    data: {
      title: "贷款产品匹配智导(通知器)",
      type: "creditCheckNode",
      order: 4,
      condition: "产品匹配结果",
      name: "productMatching",
    },
  },
  {
    id: "5",
    type: "creditCheckNode",
    position: { x: 100, y: 650 },
    data: {
      title: "材料上传与OCR智导",
      type: "creditCheckNode",
      order: 5,
      condition: "材料处理结果",
      name: "documentOCR",
    },
  },
  {
    id: "6",
    type: "creditCheckNode",
    position: { x: 100, y: 800 },
    data: {
      title: "合同生成与签署智导",
      type: "creditCheckNode",
      order: 6,
      condition: "合同处理结果",
      name: "contractGeneration",
    },
  },
  {
    id: "7",
    type: "endNode",
    position: { x: 100, y: 950 },
    data: {
      title: "还款计划生成智导",
      type: "endNode",
      order: 7,
      message: "还款计划生成完成",
    },
  },
  // 分支B：人工审核路由智导
  {
    id: "8",
    type: "endNode",
    position: { x: 700, y: 500 },
    data: {
      title: "人工审核路由智导(状态机)",
      type: "endNode",
      order: 8,
      message: "转入人工审核流程",
    },
  },
  // 分支C：直接拒绝
  {
    id: "9",
    type: "endNode",
    position: { x: 400, y: 500 },
    data: {
      title: "直接拒绝(通知器)",
      type: "endNode",
      order: 9,
      message: "申请被拒绝",
    },
  },
];

const initialEdges: Edge[] = [
  {
    id: "1-2",
    source: "1",
    target: "2",
    type: "smoothstep",
  },
  {
    id: "2-3",
    source: "2",
    target: "3",
    type: "smoothstep",
  },
  // 分流器的三个分支
  {
    id: "3-4", // 分支A：通过预审
    source: "3",
    target: "4",
    type: "smoothstep",
    sourceHandle: "approve",
    label: "通过预审",
  },
  {
    id: "3-8", // 分支B：人工审核
    source: "3",
    target: "8",
    type: "smoothstep",
    sourceHandle: "manual",
    label: "人工审核",
  },
  {
    id: "3-9", // 分支C：直接拒绝
    source: "3",
    target: "9",
    type: "smoothstep",
    sourceHandle: "reject",
    label: "直接拒绝",
  },
  // 分支A的流程链
  {
    id: "4-5",
    source: "4",
    target: "5",
    type: "smoothstep",
  },
  {
    id: "5-6",
    source: "5",
    target: "6",
    type: "smoothstep",
  },
  {
    id: "6-7",
    source: "6",
    target: "7",
    type: "smoothstep",
  },
];

function Flow() {
  const [mounted, setMounted] = useState(false);
  const [nodes, setNodes] = useState<Node<CustomNodeData>[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [creditCheckScore, setCreditCheckScore] = useState("600");
  const [debtRatio, setDebtRatio] = useState("75");
  const [expectedDifference, setExpectedDifference] = useState("5");
  const [selectedMode, setSelectedMode] = useState<"ai" | "history">("ai");
  const { getViewport } = useReactFlow();

  // 初始化节点和边
  useEffect(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
    setMounted(true);
  }, []);

  // 监听信用标准分变化
  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.data.title === "分流器") {
          return {
            ...node,
            data: {
              ...node.data,
              creditCheckScore: parseInt(creditCheckScore) || 600,
              debtRatio: parseInt(debtRatio) || 75,
              expectedDifference: parseInt(expectedDifference) || 5,
            },
          };
        }
        return node;
      })
    );
  }, [creditCheckScore, debtRatio, expectedDifference]);

  // 更新节点状态
  const updateNodeStatus = useCallback(
    (nodeId: string, status: "success" | "loading" | "error") => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                status,
                creditCheckScore: creditCheckScore
                  ? parseInt(creditCheckScore)
                  : 600,
              },
            };
          }
          return node;
        })
      );
    },
    [creditCheckScore]
  );

  // 根据节点位置重新计算 order
  const recalculateOrder = useCallback(
    (nodes: Node<CustomNodeData>[]) => {
      const sortedNodes = [...nodes].sort(
        (a, b) => a.position.y - b.position.y
      );
      return sortedNodes.map((node, index) => ({
        ...node,
        data: {
          ...node.data,
          order: index + 1,
          creditCheckScore: creditCheckScore ? parseInt(creditCheckScore) : 600,
        },
      }));
    },
    [creditCheckScore]
  );

  // 执行节点
  const executeNode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (!node) return;

      // 设置当前节点为 loading，并更新征信标准分
      setNodes((nds) =>
        nds.map((n) => {
          if (n.id === nodeId) {
            return {
              ...n,
              data: {
                ...n.data,
                status: "loading",
                creditCheckScore: creditCheckScore
                  ? parseInt(creditCheckScore)
                  : 600,
              },
            };
          }
          return n;
        })
      );

      // 模拟不同节点的处理逻辑
      setTimeout(() => {
        let status: "success" | "error" = "success";
        let nextNodeId: string | undefined;

        switch (node.data.name) {
          case "userInfoCollection":
            status = "success";
            nextNodeId = "2";
            break;
          case "riskPreCheck":
            status = "success";
            nextNodeId = "3";
            break;
          case "routingDecision":
            status = "success";
            // 根据某种逻辑决定走哪个分支，这里简化为随机
            const random = Math.random();
            if (random < 0.5) {
              nextNodeId = "4"; // 分支A：通过预审
            } else if (random < 0.8) {
              nextNodeId = "8"; // 分支B：人工审核
            } else {
              nextNodeId = "9"; // 分支C：直接拒绝
            }
            break;
          case "productMatching":
            status = "success";
            nextNodeId = "5";
            break;
          case "documentOCR":
            status = "success";
            nextNodeId = "6";
            break;
          case "contractGeneration":
            status = "success";
            nextNodeId = "7";
            break;

          default:
            status = "success";
        }

        // 更新节点状态，同时更新征信标准分
        setNodes((nds) =>
          nds.map((n) => {
            if (n.id === nodeId) {
              return {
                ...n,
                data: {
                  ...n.data,
                  status,
                  creditCheckScore: creditCheckScore
                    ? parseInt(creditCheckScore)
                    : 600,
                },
              };
            }
            return n;
          })
        );

        // 2秒后清除当前节点状态，并执行下一个节点
        setTimeout(() => {
          setNodes((nds) =>
            nds.map((n) => {
              if (n.id === nodeId) {
                return {
                  ...n,
                  data: {
                    ...n.data,
                    status: "loading",
                    creditCheckScore: creditCheckScore
                      ? parseInt(creditCheckScore)
                      : 600,
                  },
                };
              }
              return n;
            })
          );

          if (nextNodeId) {
            executeNode(nextNodeId);
          } else {
            const nextNode = nodes.find(
              (n) => n.data.order === node.data.order + 1
            );
            if (nextNode) {
              executeNode(nextNode.id);
            }
          }
        }, 2000);
      }, 2000);
    },
    [nodes, creditCheckScore]
  );

  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      setNodes((nds) => {
        const updatedNodes = applyNodeChanges(
          changes,
          nds
        ) as Node<CustomNodeData>[];
        // 如果节点位置发生变化，重新计算 order
        if (changes.some((change) => change.type === "position")) {
          return recalculateOrder(updatedNodes);
        }
        return updatedNodes;
      });
    },
    [recalculateOrder]
  );

  const onAddNode = useCallback(
    (type: string) => {
      const viewport = getViewport();
      const newNode: Node<CustomNodeData> = {
        id: `${Date.now()}`,
        type: type,
        position: {
          x: -viewport.x + 200,
          y: -viewport.y + 200,
        },
        data: {
          title:
            type === "decisionNode" ? "决策节点" : `节点 ${nodes.length + 1}`,
          type: type,
          order: nodes.length + 1,
        },
        draggable: true,
      };

      // 添加新节点后重新计算所有节点的 order
      setNodes((nds) => recalculateOrder([...nds, newNode]));
    },
    [getViewport, nodes.length, recalculateOrder]
  );

  const onEdgesChange = useCallback((changes: EdgeChange[]) => {
    setEdges((eds) => applyEdgeChanges(changes, eds));
  }, []);

  const onConnect = useCallback((connection: Connection) => {
    setEdges((eds) => addEdge({ ...connection, type: "buttonedge" }, eds));
  }, []);

  // 处理模式切换
  const handleModeChange = useCallback((mode: "ai" | "history") => {
    setSelectedMode(mode);
    // 这里可以添加不同模式的逻辑
    if (mode === "ai") {
      console.log("切换到AI模式");
      // 可以在这里添加AI模式的特定逻辑
    } else {
      console.log("切换到历史模式");
      // 可以在这里添加历史模式的特定逻辑
    }
  }, []);

  return (
    <div style={{ width: "100%", height: "100%", display: "flex" }}>
      <Sidebar onAddNode={onAddNode} />
      {mounted ? (
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onNodesChange={(changes) => {
            setNodes((nds) => {
              const updatedNodes = applyNodeChanges(changes, nds);
              if (changes.some((change) => change.type === "position")) {
                return recalculateOrder(updatedNodes);
              }
              return updatedNodes;
            });
          }}
          onEdgesChange={(changes) =>
            setEdges((eds) => applyEdgeChanges(changes, eds))
          }
          onConnect={(connection) =>
            setEdges((eds) => addEdge(connection, eds))
          }
          connectionMode={ConnectionMode.Loose}
          fitView
          style={{ width: "100%", height: "100%" }}
        >
          <Background />
          <div className="absolute top-4 left-4 z-10">
            <div className="flex gap-4 items-start">
              <div className="flex flex-col gap-3">
                <div className="flex flex-col gap-2">
                  <Label>信用标准分</Label>
                  <Input
                    type="number"
                    value={creditCheckScore}
                    onChange={(e) => setCreditCheckScore(e.target.value)}
                    placeholder="请输入信用标准分"
                    className="w-40"
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <Label>负债比</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      value={debtRatio}
                      onChange={(e) => setDebtRatio(e.target.value)}
                      placeholder="负债比"
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">%</span>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Label>期望差</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      value={expectedDifference}
                      onChange={(e) => setExpectedDifference(e.target.value)}
                      placeholder="期望差"
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">W</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Button
                  variant={selectedMode === "ai" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleModeChange("ai")}
                  className="min-w-[60px]"
                >
                  AI
                </Button>
                <Button
                  variant={selectedMode === "history" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleModeChange("history")}
                  className="min-w-[60px]"
                >
                  历史
                </Button>
              </div>
            </div>
          </div>
        </ReactFlow>
      ) : null}
    </div>
  );
}

export default function Case2() {
  return (
    <div style={{ width: "100vw", height: "100vh" }}>
      <ReactFlowProvider>
        <Flow />
      </ReactFlowProvider>
    </div>
  );
}
