import { memo, useState, useEffect } from "react";
import { <PERSON>le, Position, NodeProps, useReactFlow } from "@xyflow/react";
import BaseNode from "../base-node";
import { NodeHeader } from "@/src/components/node-header";
import { Play, Trash2 } from "lucide-react";
import { NodeStatusIndicator } from "@/src/components/node-status-indicator";
import { toast } from "sonner";
// import { TEST_CASES } from "@/src/config/test-data";
import { executeNodeWithResult, NodeData } from "@/src/utils/node-executor";
import { request } from "@/src/utils/api";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "../ui/drawer";
import { Button } from "../ui/button";

type CreditCheckNodeProps = NodeProps<NodeData>;

// 从测试数据中获取信用评分
const fetchCreditScore = async (inputValue: string): Promise<number> => {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 从测试数据中获取信用评分
  const testCase = TEST_CASES[inputValue];
  if (!testCase) {
    throw new Error("未找到对应的测试数据");
  }

  return testCase.creditScore;
};

// 新增 CreditCheckNodeDrawer 组件
function CreditCheckNodeDrawer({
  isOpen,
  onOpenChange,
  title,
  nodeName,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  nodeName?: string;
}) {
  // 根据节点名称获取不同的内容
  const getNodeContent = () => {
    switch (nodeName) {
      case "productMatching":
        return {
          title: "贷款产品匹配智导说明",
          description: "根据用户信息和风控结果匹配最适合的贷款产品",
          inputParams: [
            { name: "用户信息", desc: "用户基本信息和征信数据" },
            { name: "风控结果", desc: "风控预审智导的输出结果" },
          ],
          outputParams: [
            { name: "匹配产品", desc: "推荐的贷款产品列表" },
            { name: "产品参数", desc: "利率、期限、额度等产品参数" },
          ],
        };
      case "documentOCR":
        return {
          title: "材料上传与OCR智导说明",
          description: "处理用户上传的身份证、银行流水等材料并进行OCR识别",
          inputParams: [
            { name: "身份证件", desc: "身份证正反面照片" },
            { name: "银行流水", desc: "近6个月银行流水单据" },
            { name: "收入证明", desc: "工资单或收入证明文件" },
          ],
          outputParams: [
            { name: "OCR结果", desc: "识别出的文字信息" },
            { name: "验证状态", desc: "材料真实性验证结果" },
          ],
        };
      case "contractGeneration":
        return {
          title: "合同生成与签署智导说明",
          description: "根据审批结果生成贷款合同并处理电子签署",
          inputParams: [
            { name: "审批结果", desc: "贷款审批通过的详细信息" },
            { name: "产品参数", desc: "确定的贷款产品参数" },
            { name: "用户信息", desc: "完整的用户资料信息" },
          ],
          outputParams: [
            { name: "电子合同", desc: "生成的贷款合同文件" },
            { name: "签署状态", desc: "合同签署完成状态" },
          ],
        };
      default:
        return {
          title: "征信查询节点说明",
          description: "对接征信机构查询个人征信报告",
          inputParams: [{ name: "用户ID", desc: "AES加密" }],
          outputParams: [
            { name: "信用评分", desc: "信用风险进行量化评估的数值指标" },
            { name: "逾期记录", desc: "近2年还款记录逾期次数" },
            { name: "负债率", desc: "总负债/年收入" },
          ],
        };
    }
  };

  const content = getNodeContent();

  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange} direction="right">
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>{content.title}</DrawerTitle>
          <DrawerDescription>{content.description}</DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <h4 className="text-md font-semibold mb-2">输入参数</h4>
              <div className="border rounded-md overflow-hidden">
                {content.inputParams.map((param, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 border-b last:border-b-0"
                  >
                    <div className="p-2 border-r flex items-center justify-center">
                      {param.name}
                    </div>
                    <div className="p-2 flex items-center justify-center">
                      {param.desc}
                    </div>
                  </div>
                ))}
              </div>
              <h4 className="text-md font-semibold mt-4 mb-2">输出参数</h4>
              <div className="border rounded-md overflow-hidden">
                {content.outputParams.map((param, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 border-b last:border-b-0"
                  >
                    <div className="p-2 border-r flex items-center justify-center">
                      {param.name}
                    </div>
                    <div className="p-2 flex items-center justify-center">
                      {param.desc}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">关闭</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

const CreditCheckNode = memo((props: CreditCheckNodeProps) => {
  const { data, id } = props;
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [status, setStatus] = useState<
    "success" | "loading" | "error" | undefined
  >(undefined);
  const [isExecuting, setIsExecuting] = useState(false);
  const [score, setScore] = useState<number | undefined>(data.score);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // 执行节点的函数
  const executeNode = async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    setStatus("loading");

    try {
      // 根据节点名称决定处理逻辑
      const nodeData = data as NodeData;

      if (
        nodeData.name === "riskPreCheck" ||
        nodeData.name === "productMatching" ||
        nodeData.name === "documentOCR" ||
        nodeData.name === "contractGeneration"
      ) {
        // 对于case2中的特殊节点，不调用征信查询API，直接使用公共执行方法
        console.log(`${nodeData.name} - 使用公共执行方法处理`);
        setStatus("success");

        // 使用公共执行方法处理节点逻辑
        await executeNodeWithResult(
          id,
          700, // 默认信用分数
          "creditCheckNode",
          setNodes,
          getNodes,
          getEdges,
          600 // 默认征信标准分
        );
      } else {
        // 原有的征信查询逻辑（仅用于其他页面）
        const response = await request<{
          code: string;
          msg: string;
          data: {
            credit_score: number;
            overdue_records: number;
            debt_ratio: number;
          };
        }>({
          method: "POST",
          url: "/events/system/createInstance",
          data: {
            name: "征信查询",
            inputParams: [data.inputValue as string],
          },
        });

        const creditScore = response.data.credit_score;
        setScore(creditScore);
        setStatus("success");

        // 使用公共执行方法处理节点逻辑
        await executeNodeWithResult(
          id,
          creditScore,
          "creditCheckNode",
          setNodes,
          getNodes,
          getEdges,
          600
        );
      }

      // 清除当前节点的状态
      setStatus(undefined);
      setIsExecuting(false);
    } catch (error) {
      setStatus("error");
      toast.error("节点执行失败");
      setIsExecuting(false);
    }
  };

  // 监听父组件传入的 status
  useEffect(() => {
    if (data.status === "loading" && !isExecuting) {
      executeNode();
    }
  }, [data.status]);

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    );
  };

  const onExecute = () => {
    // 清除所有节点的状态
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          status: undefined,
        },
      }))
    );
    // 设置当前节点为 loading
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              status: "loading",
            },
          };
        }
        return node;
      })
    );
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDrawerOpen(true);
  };

  const content = (
    <div onContextMenu={handleContextMenu}>
      <BaseNode {...props} data={data as Record<string, unknown>} id={id}>
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="target"
        />
        <NodeHeader>
          <div className="flex items-center justify-between">
            <span>{data.title}</span>
            <div className="flex gap-1">
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onExecute}
                disabled={isExecuting}
              >
                <Play className="w-4 h-4" />
              </button>
              <button
                className="p-1 hover:bg-accent rounded-md"
                onClick={onDelete}
                disabled={isExecuting}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </NodeHeader>
        <CreditCheckNodeDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          title={data.title ?? "征信查询"}
          nodeName={(data as any).name}
        />
        <div className="absolute -right-24 top-1/2 transform -translate-y-1/2">
          <div className="bg-white border border-gray-200 rounded-full px-4 py-2 shadow-sm">
            <span className="text-sm font-medium">信用评分：{score}</span>
          </div>
        </div>
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary"
          isConnectable={true}
          id="source"
        />
      </BaseNode>
    </div>
  );

  if (status) {
    return <NodeStatusIndicator status={status}>{content}</NodeStatusIndicator>;
  }
  return content;
});

CreditCheckNode.displayName = "CreditCheckNode";

export default CreditCheckNode;
