import { But<PERSON> } from "@/src/components/ui/button"
import { Plus, GitBranch, Rocket, Flag } from "lucide-react"

interface SidebarProps {
    onAddNode: (type: string) => void;
}

export function Sidebar({ onAddNode }: SidebarProps) {
    return (
        <div className="w-16 border-r bg-background p-2 flex flex-col gap-2">
            <Button
                variant="outline"
                size="icon"
                className="w-full"
                onClick={() => onAddNode('startNode')}
                title="添加开始节点"
            >
                <Rocket className="h-4 w-4" />
            </Button>
            <Button
                variant="outline"
                size="icon"
                className="w-full"
                onClick={() => onAddNode('nodeWithStatus')}
                title="添加普通节点"
            >
                <Plus className="h-4 w-4" />
            </Button>
            <Button
                variant="outline"
                size="icon"
                className="w-full"
                onClick={() => onAddNode('decisionNode')}
                title="添加条件节点"
            >
                <GitBranch className="h-4 w-4" />
            </Button>
            <Button
                variant="outline"
                size="icon"
                className="w-full"
                onClick={() => onAddNode('endNode')}
                title="添加结束节点"
            >
                <Flag className="h-4 w-4" />
            </Button>
        </div>
    )
} 