import {
    BaseEdge,
    <PERSON>LabelR<PERSON><PERSON>,
    getBezierPath,
    useReactFlow,
    type EdgeProps,
    Position,
} from '@xyflow/react';
import { useCallback } from 'react';

export default function ButtonEdge({
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    style = {},
    markerEnd,
    source,
    target,
}: EdgeProps) {
    const [edgePath, labelX, labelY] = getBezierPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
    });

    const { setNodes, setEdges, getNode } = useReactFlow();

    const onEdgeClick = useCallback((evt: React.MouseEvent) => {
        evt.stopPropagation();

        // 获取源节点和目标节点
        const sourceNode = getNode(source);
        const targetNode = getNode(target);

        if (!sourceNode || !targetNode) return;

        // 计算新节点的位置（在两个节点之间）
        const newNodePosition = {
            x: (sourceNode.position.x + targetNode.position.x) / 2,
            y: (sourceNode.position.y + targetNode.position.y) / 2,
        };

        // 创建新节点
        const newNodeId = `${Date.now()}`;
        const newNode = {
            id: newNodeId,
            type: 'nodeWithStatus',
            position: newNodePosition,
            data: {
                title: '新节点',
                type: 'nodeWithStatus',
                order: 0, // 临时 order，稍后会重新计算
            },
            draggable: true,
        };

        // 创建新的边
        const newEdge1 = {
            id: `${source}-${newNodeId}`,
            source: source,
            target: newNodeId,
            type: 'buttonedge',
        };

        const newEdge2 = {
            id: `${newNodeId}-${target}`,
            source: newNodeId,
            target: target,
            type: 'buttonedge',
        };

        // 更新节点和边
        setNodes((nodes) => {
            // 添加新节点
            const updatedNodes = [...nodes, newNode];
            // 按照 y 坐标排序并重新计算 order
            return updatedNodes
                .sort((a, b) => a.position.y - b.position.y)
                .map((node, index) => ({
                    ...node,
                    data: {
                        ...node.data,
                        order: index + 1,
                    },
                }));
        });

        setEdges((edges) => {
            const filteredEdges = edges.filter((e) => e.id !== id);
            return [...filteredEdges, newEdge1, newEdge2];
        });
    }, [id, source, target, getNode, setNodes, setEdges]);

    return (
        <>
            <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
            <EdgeLabelRenderer>
                <div
                    className="absolute"
                    style={{
                        transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
                        pointerEvents: 'all',
                    }}
                >
                    <button
                        className="nodrag nopan rounded-full bg-white border border-gray-300 w-6 h-6 flex items-center justify-center hover:bg-gray-100"
                        onClick={onEdgeClick}
                    >
                        +
                    </button>
                </div>
            </EdgeLabelRenderer>
        </>
    );
} 