"use client";

import "@xyflow/react/dist/style.css";
import {
  Background,
  React<PERSON>low,
  Node,
  useReact<PERSON>low,
  ReactFlowProvider,
  NodeChange,
  applyNodeChanges,
  Edge,
  EdgeChange,
  applyEdgeChanges,
  addEdge,
  Connection,
  ConnectionMode,
  NodeTypes,
} from "@xyflow/react";
import NodeHeaderDemo from "@/src/components/node-header-demo";
import NodeWithStatus from "@/src/components/node-with-status";
import ButtonEdge from "@/src/components/button-edge";
import { Sidebar } from "@/src/components/Sidebar";
import { useState, useCallback, useEffect } from "react";
import StartNode from "@/src/components/nodes/start-node";
import DecisionNode from "@/src/components/nodes/decision-node";
import EndNode from "@/src/components/nodes/end-node";
import CreditCheckNode from "@/src/components/nodes/credit-check-node";
import AutoApprovalNode from "@/src/components/nodes/auto-approval-node";
import { Label } from "@/src/components/ui/label";
import { Input } from "@/src/components/ui/input";

interface CustomNodeData extends Record<string, unknown> {
  title: string;
  type: string;
  status?: "success" | "loading" | "error";
  order: number;
  name?: string;
  condition?: string;
  inputLabel?: string;
  score?: number;
  validateInput?: (input: string) => { valid: boolean; message: string };
  attemptCount?: number;
  creditCheckScore?: number;
  [key: string]: unknown;
}

const nodeTypes = {
  nodeHeaderNode: NodeHeaderDemo,
  nodeWithStatus: NodeWithStatus,
  startNode: StartNode,
  decisionNode: DecisionNode,
  endNode: EndNode,
  creditCheckNode: CreditCheckNode,
  autoApprovalNode: AutoApprovalNode,
} as NodeTypes;

const edgeTypes = {
  buttonedge: ButtonEdge,
};

const initialNodes: Node<CustomNodeData>[] = [
  {
    id: "1",
    type: "startNode",
    position: { x: 400, y: 50 },
    data: {
      title: "个人贷款申请",
      type: "startNode",
      order: 1,
      name: "start",
      inputLabel: "请输入6位ID",
      validateInput: (input: string) => {
        if (!/^\d{6}$/.test(input)) {
          return { valid: false, message: "请输入6位数字ID" };
        }
        return { valid: true, message: "" };
      },
    },
  },
  {
    id: "2",
    type: "creditCheckNode",
    position: { x: 400, y: 200 },
    data: {
      title: "征信查询",
      type: "creditCheckNode",
      order: 2,
      condition: "征信查询结果",
      name: "creditCheck",
    },
  },
  {
    id: "3",
    type: "decisionNode",
    position: { x: 400, y: 350 },
    data: {
      title: "自动审核可否判定",
      type: "decisionNode",
      order: 3,
      condition: "自动审核可否判定",
      name: "creditDataCheck",
      attemptCount: 0,
    },
  },
  {
    id: "4",
    type: "decisionNode",
    position: { x: 200, y: 500 },
    data: {
      title: "贷款额度自动审核",
      type: "decisionNode",
      order: 4,
      condition: "额度审核判定",
      name: "amountDataCheck",
      attemptCount: 0,
    },
  },
  {
    id: "5",
    type: "endNode",
    position: { x: 600, y: 500 },
    data: {
      title: "人工审核流程",
      type: "endNode",
      order: 5,
      message: "进入人工审核流程",
    },
  },
  {
    id: "6",
    type: "endNode",
    position: { x: 50, y: 650 },
    data: {
      title: "审核失败",
      type: "endNode",
      order: 6,
      message: "拒绝贷款申请",
    },
  },
  {
    id: "7",
    type: "endNode",
    position: { x: 350, y: 650 },
    data: {
      title: "审核通过",
      type: "endNode",
      order: 7,
      message: "贷款申请通过",
    },
  },
];

const initialEdges: Edge[] = [
  {
    id: "1-2",
    source: "1",
    target: "2",
    type: "smoothstep",
  },
  {
    id: "2-3",
    source: "2",
    target: "3",
    type: "smoothstep",
  },
  {
    id: "3-4",
    source: "3",
    target: "4",
    type: "smoothstep",
    sourceHandle: "true",
  },
  {
    id: "3-5",
    source: "3",
    target: "5",
    type: "smoothstep",
    sourceHandle: "false",
  },
  {
    id: "4-6",
    source: "4",
    target: "6",
    type: "smoothstep",
    sourceHandle: "false",
  },
  {
    id: "4-7",
    source: "4",
    target: "7",
    type: "smoothstep",
    sourceHandle: "true",
  },
];

function Flow() {
  const [mounted, setMounted] = useState(false);
  const [nodes, setNodes] = useState<Node<CustomNodeData>[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [creditCheckScore, setCreditCheckScore] = useState("600");
  const { getViewport } = useReactFlow();

  // 初始化节点和边
  useEffect(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
    setMounted(true);
  }, []);

  // 监听征信标准分变化
  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.data.title === "自动审核可否判定") {
          return {
            ...node,
            data: {
              ...node.data,
              creditCheckScore: parseInt(creditCheckScore) || 600,
            },
          };
        }
        return node;
      })
    );
  }, [creditCheckScore]);

  // 更新节点状态
  const updateNodeStatus = useCallback(
    (nodeId: string, status: "success" | "loading" | "error") => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                status,
                creditCheckScore: creditCheckScore
                  ? parseInt(creditCheckScore)
                  : 600,
              },
            };
          }
          return node;
        })
      );
    },
    [creditCheckScore]
  );

  // 根据节点位置重新计算 order
  const recalculateOrder = useCallback(
    (nodes: Node<CustomNodeData>[]) => {
      const sortedNodes = [...nodes].sort(
        (a, b) => a.position.y - b.position.y
      );
      return sortedNodes.map((node, index) => ({
        ...node,
        data: {
          ...node.data,
          order: index + 1,
          creditCheckScore: creditCheckScore ? parseInt(creditCheckScore) : 600,
        },
      }));
    },
    [creditCheckScore]
  );

  // 执行节点
  const executeNode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (!node) return;

      // 设置当前节点为 loading，并更新征信标准分
      setNodes((nds) =>
        nds.map((n) => {
          if (n.id === nodeId) {
            return {
              ...n,
              data: {
                ...n.data,
                status: "loading",
                creditCheckScore: creditCheckScore
                  ? parseInt(creditCheckScore)
                  : 600,
              },
            };
          }
          return n;
        })
      );

      // 模拟不同节点的处理逻辑
      setTimeout(() => {
        let status: "success" | "error" = "success";
        let nextNodeId: string | undefined;

        switch (node.data.name) {
          case "start":
            status = "success";
            nextNodeId = "2";
            break;
          case "creditCheck":
            status = "success";
            nextNodeId = "3";
            break;
          case "creditDataCheck":
            status = "success";
            nextNodeId = "4";
            break;
          case "amountDataCheck":
            status = "success";
            break;
          default:
            status = "success";
        }

        // 更新节点状态，同时更新征信标准分
        setNodes((nds) =>
          nds.map((n) => {
            if (n.id === nodeId) {
              return {
                ...n,
                data: {
                  ...n.data,
                  status,
                  creditCheckScore: creditCheckScore
                    ? parseInt(creditCheckScore)
                    : 600,
                },
              };
            }
            return n;
          })
        );

        // 2秒后清除当前节点状态，并执行下一个节点
        setTimeout(() => {
          setNodes((nds) =>
            nds.map((n) => {
              if (n.id === nodeId) {
                return {
                  ...n,
                  data: {
                    ...n.data,
                    status: "loading",
                    creditCheckScore: creditCheckScore
                      ? parseInt(creditCheckScore)
                      : 600,
                  },
                };
              }
              return n;
            })
          );

          if (nextNodeId) {
            executeNode(nextNodeId);
          } else {
            const nextNode = nodes.find(
              (n) => n.data.order === node.data.order + 1
            );
            if (nextNode) {
              executeNode(nextNode.id);
            }
          }
        }, 2000);
      }, 2000);
    },
    [nodes, creditCheckScore]
  );

  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      setNodes((nds) => {
        const updatedNodes = applyNodeChanges(
          changes,
          nds
        ) as Node<CustomNodeData>[];
        // 如果节点位置发生变化，重新计算 order
        if (changes.some((change) => change.type === "position")) {
          return recalculateOrder(updatedNodes);
        }
        return updatedNodes;
      });
    },
    [recalculateOrder]
  );

  const onAddNode = useCallback(
    (type: string) => {
      const viewport = getViewport();
      const newNode: Node<CustomNodeData> = {
        id: `${Date.now()}`,
        type: type,
        position: {
          x: -viewport.x + 200,
          y: -viewport.y + 200,
        },
        data: {
          title:
            type === "decisionNode" ? "决策节点" : `节点 ${nodes.length + 1}`,
          type: type,
          order: nodes.length + 1,
        },
        draggable: true,
      };

      // 添加新节点后重新计算所有节点的 order
      setNodes((nds) => recalculateOrder([...nds, newNode]));
    },
    [getViewport, nodes.length, recalculateOrder]
  );

  const onEdgesChange = useCallback((changes: EdgeChange[]) => {
    setEdges((eds) => applyEdgeChanges(changes, eds));
  }, []);

  const onConnect = useCallback((connection: Connection) => {
    setEdges((eds) => addEdge({ ...connection, type: "buttonedge" }, eds));
  }, []);

  return (
    <div style={{ width: "100%", height: "100%", display: "flex" }}>
      <Sidebar onAddNode={onAddNode} />
      {mounted ? (
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onNodesChange={(changes) => {
            setNodes((nds) => {
              const updatedNodes = applyNodeChanges(changes, nds);
              if (changes.some((change) => change.type === "position")) {
                return recalculateOrder(updatedNodes);
              }
              return updatedNodes;
            });
          }}
          onEdgesChange={(changes) =>
            setEdges((eds) => applyEdgeChanges(changes, eds))
          }
          onConnect={(connection) =>
            setEdges((eds) => addEdge(connection, eds))
          }
          connectionMode={ConnectionMode.Loose}
          fitView
          style={{ width: "100%", height: "100%" }}
        >
          <Background />
          <div className="absolute top-4 left-4 z-10">
            <div className="flex flex-col gap-2">
              <Label>征信标准分</Label>
              <Input
                type="number"
                value={creditCheckScore}
                onChange={(e) => setCreditCheckScore(e.target.value)}
                placeholder="请输入征信标准分"
              />
            </div>
          </div>
        </ReactFlow>
      ) : null}
    </div>
  );
}

export default function Home() {
  return (
    <div style={{ width: "100vw", height: "100vh" }}>
      <ReactFlowProvider>
        <Flow />
      </ReactFlowProvider>
    </div>
  );
}
